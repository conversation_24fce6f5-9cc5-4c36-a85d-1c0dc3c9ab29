["response prob || post-trial period || blank trial only || 1std", "latency || post-trial period || all trial included", "latency || post-trial period || stimulus trial only", "peak || pre-trial period || blank trial only", "peak || pre-trial period || all trial included", "auc || evoked-trial period || stimulus trial only", "peak || evoked-trial period || stimulus trial only", "response prob || evoked-trial period || stimulus trial only || 3std", "peak || evoked-trial period || all trial included", "amplitude || inter-trial blocks || 1std", "response prob || evoked-trial period || stimulus trial only || 5std", "response prob || post-trial period || stimulus trial only || 5std", "response prob || evoked-trial period || all trial included || 5std", "auc || pre-trial period || all trial included", "auc || evoked-trial period || all trial included", "peak || pre-trial period || stimulus trial only", "amplitude || init block || 1std", "response prob || evoked-trial period || all trial included || 3std", "response prob || post-trial period || stimulus trial only || 3std", "response prob || evoked-trial period || stimulus trial only || 2std", "amplitude || inter-trial blocks || 2std", "response prob || post-trial period || stimulus trial only || 10std", "response prob || evoked-trial period || stimulus trial only || 10std", "auc || inter-trial blocks", "amplitude || init&final blocks || 1std", "peak || post-trial period || stimulus trial only", "response prob || evoked-trial period || all trial included || 10std", "response prob || post-trial period || all trial included || 5std", "peak || post-trial period || all trial included", "response prob || pre-trial period || blank trial only || 1std", "count || init block || 1std", "auc || init&final blocks", "auc || init block", "count || init&final blocks || 1std", "response prob || post-trial period || blank trial only || 2std", "response prob || evoked-trial period || stimulus trial only || 1std", "count || init block || 2std", "count || final block || 1std", "amplitude || init block || 2std", "count || inter-trial blocks || 1std", "auc || post-trial period || stimulus trial only", "response prob || pre-trial period || all trial included || 1std", "response prob || post-trial period || all trial included || 10std", "amplitude || init&final blocks || 2std", "count || inter-trial blocks || 2std", "count || init&final blocks || 2std", "auc || pre-trial period || blank trial only", "response prob || post-trial period || stimulus trial only || 2std", "amplitude || init&final blocks || 5std", "amplitude || init block || 5std", "latency || post-trial period || blank trial only", "count || final block || 2std", "response prob || evoked-trial period || blank trial only || 1std", "auc || post-trial period || all trial included", "auc || final block", "response prob || post-trial period || all trial included || 3std", "response prob || evoked-trial period || all trial included || 2std", "response prob || pre-trial period || blank trial only || 5std", "amplitude || inter-trial blocks || 5std", "auc || pre-trial period || stimulus trial only", "response prob || pre-trial period || blank trial only || 10std", "response prob || post-trial period || all trial included || 1std", "response prob || pre-trial period || all trial included || 5std", "latency || evoked-trial period || blank trial only", "amplitude || final block || 10std", "amplitude || final block || 1std", "response prob || pre-trial period || stimulus trial only || 3std", "count || init&final blocks || 5std", "response prob || pre-trial period || stimulus trial only || 5std", "count || final block || 10std", "amplitude || final block || 2std", "com || pre-trial period || blank trial only", "response prob || pre-trial period || all trial included || 3std", "latency || evoked-trial period || all trial included", "count || final block || 5std", "response prob || post-trial period || stimulus trial only || 1std", "response prob || pre-trial period || stimulus trial only || 1std", "response prob || evoked-trial period || blank trial only || 2std", "response prob || pre-trial period || blank trial only || 2std", "peak || evoked-trial period || blank trial only", "response prob || pre-trial period || all trial included || 10std", "peak || post-trial period || blank trial only", "com || pre-trial period || all trial included", "response prob || post-trial period || all trial included || 2std", "count || init block || 5std", "latency || pre-trial period || blank trial only", "com || post-trial period || blank trial only", "com || post-trial period || stimulus trial only", "count || init&final blocks || 10std", "response prob || post-trial period || blank trial only || 3std", "com || pre-trial period || stimulus trial only", "com || post-trial period || all trial included", "latency || pre-trial period || all trial included", "amplitude || inter-trial blocks || 10std", "response prob || evoked-trial period || all trial included || 1std", "latency || evoked-trial period || stimulus trial only", "amplitude || init block || 10std", "response prob || pre-trial period || stimulus trial only || 10std", "response prob || post-trial period || blank trial only || 10std", "amplitude || final block || 5std", "auc || post-trial period || blank trial only", "com || evoked-trial period || all trial included", "auc || evoked-trial period || blank trial only", "com || evoked-trial period || stimulus trial only", "com || evoked-trial period || blank trial only", "response prob || pre-trial period || blank trial only || 3std", "response prob || pre-trial period || stimulus trial only || 2std", "count || inter-trial blocks || 5std", "response prob || post-trial period || blank trial only || 5std", "response prob || evoked-trial period || blank trial only || 10std", "response prob || pre-trial period || all trial included || 2std", "count || inter-trial blocks || 10std", "amplitude || init&final blocks || 10std", "response prob || evoked-trial period || blank trial only || 5std", "response prob || evoked-trial period || blank trial only || 3std", "latency || pre-trial period || stimulus trial only", "count || init block || 10std"]